using System.ComponentModel.DataAnnotations;

namespace HarriTenantApp.Shared.DTOs;

/// <summary>
/// Data transfer object for creating a new HarriTenant
/// </summary>
public class CreateHarriTenantDto
{
    /// <summary>
    /// Gets or sets the tenant name
    /// </summary>
    [Required(ErrorMessage = "Name is required")]
    [StringLength(255, ErrorMessage = "Name cannot exceed 255 characters")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the tenant description
    /// </summary>
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the tenant is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Gets or sets the email address for the tenant
    /// </summary>
    [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the phone number for the tenant
    /// </summary>
    [StringLength(50, ErrorMessage = "Phone cannot exceed 50 characters")]
    public string? Phone { get; set; }
}
