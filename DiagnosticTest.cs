using System;
using System.Threading.Tasks;
using Microsoft.Playwright;

class Program
{
    static async Task Main(string[] args)
    {
        using var playwright = await Playwright.CreateAsync();
        await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions 
        { 
            Headless = false  // Show browser for debugging
        });
        var context = await browser.NewContextAsync();
        var page = await context.NewPageAsync();

        // Listen for console messages
        page.Console += (_, e) => Console.WriteLine($"Console [{e.Type}]: {e.Text}");
        
        // Listen for page errors
        page.PageError += (_, e) => Console.WriteLine($"Page Error: {e}");
        
        // Listen for network responses
        page.Response += (_, e) => Console.WriteLine($"Response: {e.Status} {e.Url}");

        try
        {
            await page.GotoAsync("http://localhost:5164");
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(10000); // Wait 10 seconds to see what happens
            
            // Check if there's error UI visible
            var errorElement = await page.QuerySelectorAsync("#blazor-error-ui");
            if (errorElement != null)
            {
                var errorDisplay = await errorElement.GetAttributeAsync("style");
                if (string.IsNullOrEmpty(errorDisplay) || !errorDisplay.Contains("display: none"))
                {
                    Console.WriteLine("Blazor error UI is visible!");
                    var errorText = await errorElement.TextContentAsync();
                    Console.WriteLine($"Error text: {errorText}");
                }
            }
            
            // Check app content
            var appElement = await page.QuerySelectorAsync("#app");
            if (appElement != null)
            {
                var appContent = await appElement.InnerHTMLAsync();
                Console.WriteLine($"App content length: {appContent.Length}");
                if (appContent.Contains("loading-progress"))
                {
                    Console.WriteLine("Still showing loading screen");
                }
            }

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception: {ex}");
        }

        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
