using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Playwright;
using Xunit;
using FluentAssertions;

namespace HarriTenantApp.IntegrationTests
{    public class SmokeTests : IAsyncLifetime
    {
        private const string ApiProjectPath = @"..\..\src\HarriTenantApp.Api";
        private const string AppUrl = "http://localhost:5164";
        private const string TenantsUrl = "http://localhost:5164/tenants";
        private const string ScreenshotDir = "Screenshots";
        private Process? _apiProcess;
        private IPlaywright _playwright;
        private IBrowser _browser;
        private IBrowserContext _context;
        private IPage _page;

        public async Task InitializeAsync()
        {
            // Start the consolidated API (which serves both API and Client)
            _apiProcess = StartDotnetProject(ApiProjectPath);
            await WaitForAppReady(AppUrl);

            _playwright = await Playwright.CreateAsync();
            _browser = await _playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions { Headless = true });
            _context = await _browser.NewContextAsync();
            _page = await _context.NewPageAsync();
        }

        public async Task DisposeAsync()
        {
            await _page.CloseAsync();
            await _context.CloseAsync();
            await _browser.CloseAsync();
            _playwright.Dispose();

            if (_apiProcess != null && !_apiProcess.HasExited)
            {
                _apiProcess.Kill(true);
                _apiProcess.Dispose();
            }
        }

        [Fact(DisplayName = "Basic navigation: Load root page and navigate to tenants")]
        public async Task BasicNavigation_LoadRootAndNavigateToTenants()
        {
            // Arrange
            Directory.CreateDirectory(ScreenshotDir);
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");

            // Step 1: Load root page first
            await _page.GotoAsync(AppUrl);
            await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(3000); // Give Blazor time to initialize
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"01_Root_{timestamp}.png") });

            // Check if Blazor error UI is visible
            var errorElement = await _page.QuerySelectorAsync("#blazor-error-ui");
            if (errorElement != null)
            {
                var errorStyle = await errorElement.GetAttributeAsync("style");
                if (string.IsNullOrEmpty(errorStyle) || !errorStyle.Contains("display: none"))
                {
                    var errorText = await errorElement.TextContentAsync();
                    throw new Exception($"Blazor error detected: {errorText}");
                }
            }

            // Step 2: Wait for Blazor app to load (look for app content)
            await _page.WaitForSelectorAsync("#app", new PageWaitForSelectorOptions { Timeout = 15000 });
            
            // Check that we're not still showing the loading screen
            var appContent = await _page.InnerHTMLAsync("#app");
            if (appContent.Contains("loading-progress"))
            {
                // Wait a bit more for loading to complete
                await Task.Delay(5000);
                await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"02_StillLoading_{timestamp}.png") });
            }

            // Step 3: Try to navigate to tenants page
            await _page.GotoAsync(TenantsUrl);
            await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(5000); // Give page time to render
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"03_TenantsPage_{timestamp}.png") });

            // Step 4: Look for any content that indicates the page loaded
            try
            {
                await _page.WaitForSelectorAsync("h1, h2, h3, .container, .content", new PageWaitForSelectorOptions { Timeout = 10000 });
            }
            catch (TimeoutException)
            {
                var pageContent = await _page.ContentAsync();
                throw new Exception($"No page content found. Page HTML preview: {pageContent.Substring(0, Math.Min(1000, pageContent.Length))}");
            }

            Console.WriteLine($"Navigation test completed successfully. Screenshots saved with timestamp: {timestamp}");
        }

        [Fact(DisplayName = "End-to-end: Add and delete tenant with screenshots")]
        public async Task AddAndDeleteTenant_EndToEnd_Screenshots()
        {
            // Arrange
            Directory.CreateDirectory(ScreenshotDir);
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");            // Step 1: Go to tenant list (with proper loading)
            await _page.GotoAsync(TenantsUrl);
            await _page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            await Task.Delay(5000); // Give Blazor time to render
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"01_TenantList_{timestamp}.png") });

            // Wait for the page to be interactive (look for the Add button)
            await _page.WaitForSelectorAsync("button:has-text('Add New Tenant')", new PageWaitForSelectorOptions { Timeout = 15000 });

            // Step 2: Click 'Add New Tenant'
            await _page.ClickAsync("button:has-text('Add New Tenant')");
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"02_CreateForm_{timestamp}.png") });

            // Step 3: Fill out form with unique data
            var unique = Guid.NewGuid().ToString("N").Substring(0, 8);
            await _page.FillAsync("#name", $"TestTenant_{unique}");
            await _page.FillAsync("#client", $"TestClient_{unique}");
            await _page.FillAsync("#secret", "TestSecret123!");
            await _page.FillAsync("#baseUrl", "https://test.example.com");
            await _page.FillAsync("#tokenUrl", "https://test.example.com/token");
            await _page.CheckAsync("#isCorporate");
            await _page.CheckAsync("#isActive");
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"03_FilledForm_{timestamp}.png") });

            // Step 4: Submit form
            await _page.ClickAsync("button:has-text('Create Tenant')");
            await _page.WaitForURLAsync("**/tenants");
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"04_AfterCreate_{timestamp}.png") });

            // Step 5: Find the new tenant row and click delete
            var rowSelector = $"tr:has(td:text('TestTenant_{unique}'))";
            await _page.WaitForSelectorAsync(rowSelector);
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"05_BeforeDelete_{timestamp}.png") });
            await _page.ClickAsync($"{rowSelector} button[title='Delete Tenant']");
            await _page.WaitForSelectorAsync(".modal.show");
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"06_ConfirmDelete_{timestamp}.png") });

            // Step 6: Confirm delete
            await _page.ClickAsync(".modal.show button.btn-danger");
            await _page.WaitForSelectorAsync(".modal", new PageWaitForSelectorOptions { State = WaitForSelectorState.Detached });
            await _page.ScreenshotAsync(new PageScreenshotOptions { Path = Path.Combine(ScreenshotDir, $"07_AfterDelete_{timestamp}.png") });

            // Assert: The tenant row is gone
            var row = await _page.QuerySelectorAsync(rowSelector);
            row.Should().BeNull();
        }

        private static Process StartDotnetProject(string projectPath)
        {
            // Set explicit URLs for API and Client
            // Only start the API project (client is now served from API)
            var psi = new ProcessStartInfo("dotnet", $"run --project \"{projectPath}\" --urls=http://localhost:5164")
            {
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            };
            return Process.Start(psi)!;
        }

        private static async Task WaitForAppReady(string url, int timeoutSeconds = 60)
        {
            using var client = new System.Net.Http.HttpClient();
            var start = DateTime.Now;
            while ((DateTime.Now - start).TotalSeconds < timeoutSeconds)
            {
                try
                {
                    var resp = await client.GetAsync(url);
                    if (resp.IsSuccessStatusCode)
                    {
                        return;
                    }
                }
                catch
                {
                    // ignore
                }
                await Task.Delay(1000);
            }
            throw new TimeoutException($"App at {url} did not become ready in {timeoutSeconds} seconds.");
        }
    }
}
