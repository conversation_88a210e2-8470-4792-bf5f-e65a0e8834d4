using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HarriTenantApp.Shared.Models;

/// <summary>
/// Represents a tenant in the HarriTenant system
/// </summary>
[Table("HarriTenant", Schema = "dbo")]
public class HarriTenant
{
    /// <summary>
    /// Gets or sets the unique identifier for the tenant
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the tenant name
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the tenant description
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the tenant is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Gets or sets the date when the tenant was created
    /// </summary>
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the date when the tenant was last modified
    /// </summary>
    public DateTime? ModifiedDate { get; set; }

    /// <summary>
    /// Gets or sets the email address for the tenant
    /// </summary>
    [StringLength(255)]
    [EmailAddress]
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the phone number for the tenant
    /// </summary>
    [StringLength(50)]
    public string? Phone { get; set; }
}
