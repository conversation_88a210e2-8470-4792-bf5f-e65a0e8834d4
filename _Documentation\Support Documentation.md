---
purpose: "Comprehensive project documentation for human developers and support teams"
audience: "Human Developers, Support Teams, Stakeholders"
instructions: |
  Document setup procedures, deployment instructions, troubleshooting guides,
  API documentation, and user workflows. Focus on practical, actionable information
  for team members who need to understand, maintain, or support this project.
update_triggers: "When user-facing features change, deployment procedures update, or new setup requirements are added"
---

# Support Documentation

This file provides comprehensive reference information for human developers and support teams working on the HarriTenantApp project.

## Setup and Installation Instructions

### Prerequisites
- Visual Studio 2022 or later (or VS Code with C# extension)
- .NET 8.0 SDK
- SQL Server (LocalDB or full instance)
- Git

### Project Setup
1. **Clone Repository:**
   ```bash
   git clone <repository-url>
   cd HarriTenantApp\HarriTenantGithub
   ```

2. **Restore Dependencies:**
   ```bash
   dotnet restore
   ```

3. **Build Solution:**
   ```bash
   dotnet build
   ```

4. **Verify Setup:**
   All projects should build successfully without errors.

### Project Structure
- **Solution File:** `HarriTenantApp.sln`
- **API Project:** `src/HarriTenantApp.Api/` - ASP.NET Core Web API
- **Client Project:** `src/HarriTenantApp.Client/` - Blazor WebAssembly
- **Shared Library:** `src/HarriTenantApp.Shared/` - Common models/DTOs
- **Tests:** `tests/` - xUnit test projects

### Dependencies
- **Entity Framework Core:** 9.0.5 (SQL Server provider)
- **NLog:** 5.5.0 (Logging framework)
- **xUnit:** Latest (Testing framework)

## Deployment Procedures

*To be documented when deployment configurations are established*

## Troubleshooting Guides

### Common Build Issues
1. **Missing .NET 8.0 SDK:**
   - Download and install from https://dotnet.microsoft.com/download
   - Verify with: `dotnet --version`

2. **Package Restore Failures:**
   - Clear NuGet cache: `dotnet nuget locals all --clear`
   - Restore packages: `dotnet restore`

3. **Project Reference Issues:**
   - Verify all project references are correct in .csproj files
   - Rebuild solution: `dotnet build --no-incremental`

## API Documentation

*To be documented when API endpoints are implemented*

## Configuration Details

### Connection Strings
- **Development:** Configured in `appsettings.Development.json`
- **Production:** Configured in `appsettings.json`

### NLog Configuration
- Configuration file: `nlog.config` (to be created in API project)
- Log levels: Trace, Debug, Info, Warn, Error, Fatal

### Environment Variables
*To be documented as configuration requirements are identified*

## User Guides and Workflows

*To be documented when UI components are implemented*

---

**Last Updated:** June 4, 2025  
**Project Status:** Task 1 Complete - Project Setup and Structure established
