---
purpose: "AI Knowledge Base for Project Context and Patterns - Prevents Multiple Codebase Analysis"
audience: "AI Assistants (Primary), Human Developers (Secondary)"
instructions: |
  This file serves as persistent memory for AI assistants working on this project.
  Document architectural decisions, coding patterns, dependencies, known issues,
  and lessons learned to prevent redundant codebase analysis and multiple explorations
  of the same code sections. Always consult this file FIRST before examining code.
---

# My Memory

This file is the AI knowledge base for the project. Add architectural notes, patterns, dependencies, and lessons learned here.

## Project Structure (Last Updated: June 4, 2025)

### Root Directory Structure
```
c:\dev\master3\HarriTenantApp\HarriTenantAugment\
├── HarriTenantApp.sln              # Visual Studio Solution File
├── .gitignore                      # Git ignore patterns for .NET projects
├── .git/                           # Git repository
├── .Github/                        # GitHub-specific files
│   └── Instructions/               # Custom coding standards
├── _Documentation/                 # Project documentation (solution folder)
│   ├── HarrTenantPRD.md           # Product Requirements Document
│   ├── How To Guide.md            # Setup and usage guide
│   ├── My Memory.md               # AI knowledge base (this file)
│   ├── PromptTranscript.md        # Verbatim conversation log
│   ├── Support Documentation.md   # Technical support info
│   ├── Tasks.md                   # Project task breakdown
│   └── Unit Tests.md              # Unit test specifications
├── src/                           # Source code projects
│   ├── HarriTenantApp.Server/     # Blazor Server Application
│   └── HarriTenantApp.Shared/     # Shared models/contracts
└── tests/                         # Test projects
    └── HarriTenantApp.Server.Tests/ # Server unit tests
```

### Project Details

#### HarriTenantApp.Server (Blazor Server Application)
- **Framework:** .NET 8.0
- **Packages:**
  - Microsoft.EntityFrameworkCore.SqlServer (9.0.5)
  - Microsoft.EntityFrameworkCore.Tools (9.0.5)
- **References:** HarriTenantApp.Shared
- **Purpose:** Single executable Blazor Server app for CRUD operations on HarriTenant table
- **Architecture:** Direct database access, no separate API layer
- **Deployment:** Can be published as single file executable

#### HarriTenantApp.Shared (Class Library)
- **Framework:** .NET 8.0
- **Purpose:** Shared models, DTOs, and validation attributes

#### Test Projects
- **HarriTenantApp.Server.Tests:** xUnit tests for Server project
- **Framework:** .NET 8.0

## Architecture Decisions

### Technology Stack
- **Application:** Blazor Server (.NET 8.0)
- **Database:** SQL Server (local development)
- **ORM:** Entity Framework Core (Database-First approach)
- **Logging:** Built-in .NET logging (no external framework)
- **Testing:** xUnit
- **DI Container:** Microsoft.Extensions.DependencyInjection (built-in)
- **Architecture:** Single executable with direct database access

### Project References
- Server → Shared (for models/DTOs)
- Server.Tests → Server + Shared

## Task Status
- ✅ **Task 1:** Project Setup and Structure (COMPLETED - December 19, 2024)
  - **RESTRUCTURED:** Changed from Blazor WebAssembly + Web API to Blazor Server architecture
  - Solution created with Blazor Server project structure
  - Deleted API and Client projects, created Server project
  - NuGet packages installed (Entity Framework Core 9.0.5)
  - Project references configured correctly (Server → Shared)
  - Configuration files set up (appsettings.json with correct connection string)
  - .gitignore file exists and configured
  - Solution builds successfully without errors
  - **Architecture:** Single executable Blazor Server for future web portability
- ❌ **Task 2:** Database Integration and Models (NOT STARTED)
- ❌ **Task 3:** API and Repository Development (NOT STARTED)
- ❌ **Task 4:** Blazor WebAssembly UI Development (NOT STARTED)
- ❌ **Task 5:** Styling and Usability (NOT STARTED)
- ❌ **Task 6:** Testing and Quality Assurance (NOT STARTED)

## Configuration Notes

### Database Connection
- Connection string: `Server=(localdb);Database=dbNServiceBus_Employment_Config;Trusted_Connection=true;MultipleActiveResultSets=true`
- Database-first approach planned for existing HarriTenant table
- **Database:** dbNServiceBus_Employment_Config (existing database, do not modify schema)
- **Table:** [dbo].[HarriTenant] with TenantId (PK), Client, Secret, TokenUrl, BaseUrl, Name, IsCorporateTenant, IsActive
- **Validation:** All fields required for application (even if nullable in DB)

### Nullable Reference Types
- Enabled across all projects (`<Nullable>enable</Nullable>`)

### Coding Standards Applied
- Allman-style braces
- PascalCase for public members
- camelCase for parameters/locals
- XML documentation for public APIs
- Explicit access modifiers
- .NET 8.0 targeting
