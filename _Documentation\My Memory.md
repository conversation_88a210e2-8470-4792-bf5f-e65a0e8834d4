---
purpose: "AI Knowledge Base for Project Context and Patterns - Prevents Multiple Codebase Analysis"
audience: "AI Assistants (Primary), Human Developers (Secondary)"
instructions: |
  This file serves as persistent memory for AI assistants working on this project.
  Document architectural decisions, coding patterns, dependencies, known issues,
  and lessons learned to prevent redundant codebase analysis and multiple explorations
  of the same code sections. Always consult this file FIRST before examining code.
---

# My Memory

This file is the AI knowledge base for the project. Add architectural notes, patterns, dependencies, and lessons learned here.

## Project Structure (Last Updated: June 4, 2025)

### Root Directory Structure
```
c:\dev\master3\HarriTenantApp\HarriTenantGithub\
├── HarriTenantApp.sln              # Visual Studio Solution File
├── README.md
├── .gitignore                      # Git ignore patterns for .NET projects
├── .git/                           # Git repository
├── .Github/                        # GitHub-specific files
│   └── Instructions/               # Custom coding standards
├── _Documentation/                 # Project documentation (solution folder)
│   ├── HarrTenantPRD.md           # Product Requirements Document
│   ├── How To Guide.md            # Setup and usage guide
│   ├── My Memory.md               # AI knowledge base (this file)
│   ├── PromptTranscript.md        # Verbatim conversation log
│   ├── Support Documentation.md   # Technical support info
│   └── Tasks.md                   # Project task breakdown
├── src/                           # Source code projects
│   ├── HarriTenantApp.Api/        # ASP.NET Core Web API
│   ├── HarriTenantApp.Client/     # Blazor WebAssembly UI
│   └── HarriTenantApp.Shared/     # Shared models/contracts
└── tests/                         # Test projects
    ├── HarriTenantApp.Api.Tests/   # API unit tests
    └── HarriTenantApp.Client.Tests/ # Client unit tests
```

### Project Details

#### HarriTenantApp.Api (ASP.NET Core Web API)
- **Framework:** .NET 8.0
- **Packages:** 
  - Microsoft.EntityFrameworkCore.SqlServer (9.0.5)
  - Microsoft.EntityFrameworkCore.Tools (9.0.5)
- **References:** HarriTenantApp.Shared
- **Purpose:** REST API for CRUD operations on HarriTenant table
- **Note:** No logging framework installed (logging not required per user)

#### HarriTenantApp.Client (Blazor WebAssembly)
- **Framework:** .NET 8.0
- **References:** HarriTenantApp.Shared
- **Purpose:** Frontend UI for tenant management

#### HarriTenantApp.Shared (Class Library)
- **Framework:** .NET 8.0
- **Purpose:** Shared models, DTOs, and contracts between API and Client

#### Test Projects
- **HarriTenantApp.Api.Tests:** xUnit tests for API project
- **HarriTenantApp.Client.Tests:** xUnit tests for Client project
- **Framework:** .NET 8.0 (both)

## Architecture Decisions

### Technology Stack
- **Backend:** ASP.NET Core Web API (.NET 8.0)
- **Frontend:** Blazor WebAssembly (.NET 8.0)
- **Database:** SQL Server (local development)
- **ORM:** Entity Framework Core (Database-First approach)
- **Logging:** Built-in .NET logging (no external framework)
- **Testing:** xUnit
- **DI Container:** Microsoft.Extensions.DependencyInjection (built-in)

### Project References
- API → Shared (for models/DTOs)
- Client → Shared (for models/DTOs)
- Api.Tests → Api + Shared
- Client.Tests → Client + Shared

## Task Status
- ✅ **Task 1:** Project Setup and Structure (COMPLETED - December 19, 2024)
  - Solution created with proper project structure and solution folders
  - All projects added to solution with correct references
  - NuGet packages installed (Entity Framework Core 9.0.5)
  - Project references configured correctly
  - Configuration files set up (appsettings.json with connection string)
  - .gitignore file exists and configured
  - Solution builds successfully without errors
- ❌ **Task 2:** Database Integration and Models (NOT STARTED)
- ❌ **Task 3:** API and Repository Development (NOT STARTED)
- ❌ **Task 4:** Blazor WebAssembly UI Development (NOT STARTED)
- ❌ **Task 5:** Styling and Usability (NOT STARTED)
- ❌ **Task 6:** Testing and Quality Assurance (NOT STARTED)

## Configuration Notes

### Database Connection
- Connection string configured for local SQL Server
- Database-first approach planned for existing HarriTenant table

### Nullable Reference Types
- Enabled across all projects (`<Nullable>enable</Nullable>`)

### Coding Standards Applied
- Allman-style braces
- PascalCase for public members
- camelCase for parameters/locals
- XML documentation for public APIs
- Explicit access modifiers
- .NET 8.0 targeting
