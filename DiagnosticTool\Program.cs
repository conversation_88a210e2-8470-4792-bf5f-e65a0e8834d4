using System;
using System.Threading.Tasks;
using Microsoft.Playwright;

class Program
{
    static async Task Main(string[] args)
    {
        using var playwright = await Playwright.CreateAsync();
        await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions 
        { 
            Headless = false  // Show browser for debugging
        });
        var context = await browser.NewContextAsync();
        var page = await context.NewPageAsync();

        // Listen for console messages
        page.Console += (_, e) => Console.WriteLine($"Console [{e.Type}]: {e.Text}");
        
        // Listen for page errors
        page.PageError += (_, e) => Console.WriteLine($"Page Error: {e}");
        
        // Listen for network responses
        page.Response += (_, e) => Console.WriteLine($"Response: {e.Status} {e.Url}");

        try
        {
            Console.WriteLine("Navigating to http://localhost:5164...");
            await page.GotoAsync("http://localhost:5164");
            Console.WriteLine("Page loaded, waiting for network idle...");
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);
            Console.WriteLine("Network idle, waiting 10 seconds for app to initialize...");
            await Task.Delay(10000); // Wait 10 seconds to see what happens
            
            // Check if there's error UI visible
            var errorElement = await page.QuerySelectorAsync("#blazor-error-ui");
            if (errorElement != null)
            {
                var errorDisplay = await errorElement.GetAttributeAsync("style");
                if (string.IsNullOrEmpty(errorDisplay) || !errorDisplay.Contains("display: none"))
                {
                    Console.WriteLine("Blazor error UI is visible!");
                    var errorText = await errorElement.TextContentAsync();
                    Console.WriteLine($"Error text: {errorText}");
                }
                else
                {
                    Console.WriteLine("Blazor error UI is hidden (good!)");
                }
            }
            else
            {
                Console.WriteLine("No blazor error UI element found");
            }
            
            // Check app content
            var appElement = await page.QuerySelectorAsync("#app");
            if (appElement != null)
            {
                var appContent = await appElement.InnerHTMLAsync();
                Console.WriteLine($"App content length: {appContent.Length}");
                if (appContent.Contains("loading-progress"))
                {
                    Console.WriteLine("Still showing loading screen");
                }
                else
                {
                    Console.WriteLine("App appears to have loaded beyond loading screen");
                }
                
                // Print first 500 characters of app content for debugging
                var contentPreview = appContent.Length > 500 ? appContent.Substring(0, 500) + "..." : appContent;
                Console.WriteLine($"App content preview: {contentPreview}");
            }
            else
            {
                Console.WriteLine("No #app element found");
            }

            // Try to take a screenshot
            try
            {
                await page.ScreenshotAsync(new PageScreenshotOptions
                {
                    Path = "diagnostic-screenshot.png",
                    FullPage = true
                });
                Console.WriteLine("Screenshot saved as diagnostic-screenshot.png");
            }
            catch (Exception screenshotEx)
            {
                Console.WriteLine($"Failed to take screenshot: {screenshotEx.Message}");
            }

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception: {ex}");
        }

        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
